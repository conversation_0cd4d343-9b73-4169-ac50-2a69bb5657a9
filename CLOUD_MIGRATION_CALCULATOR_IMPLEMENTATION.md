# Cloud Migration Cost Calculator - QuestionAndAnswers Component Implementation

## Overview

This document outlines the implementation of a cloud migration specific variant of the QuestionAndAnswers component, maintaining consistency with the existing AI readiness feature architecture while adapting it for cloud migration calculator specific needs.

## Implementation Summary

### 1. New Component: CloudMigrationQuestionAndAnswers

**Location**: `src/components/CloudMigrationQuestionAndAnswers/`

**Key Features**:
- **Multi-select Support**: Handles checkbox-based questions for scenarios like "Which cloud environments are you planning to deploy" where multiple answers are valid
- **Single-select Support**: Maintains radio button functionality for exclusive choices
- **TypeScript Integration**: Fully typed interfaces for better development experience
- **Cloud Migration Specific Logic**: Tailored for cost calculation requirements
- **Responsive Design**: Maintains mobile-first approach with tablet and desktop breakpoints

**Multi-select Detection Logic**:
```typescript
function isMultiSelectQuestion(question: Question): boolean {
  const questionName = question.name.toLowerCase();
  return (
    questionName.includes('which cloud environments') ||
    questionName.includes('what type of workloads') ||
    questionName.includes('what is the main purpose') ||
    questionName.includes('select all that apply') ||
    questionName.includes('multiple') ||
    question.answers.some(answer => answer.name.includes('Multiple') || answer.name.includes('All'))
  );
}
```

### 2. Enhanced Form Submission Logic

**Location**: `src/hooks/useForm.ts`

**New Function**: `handleSubmitCloudMigration`

**Key Features**:
- Maps form data to match the expected payload structure from the example
- Handles both single and multi-select question responses
- Integrates with existing user tracking and IP data collection
- Submits to the existing `/api/cloud-migration-cost-calculator/` endpoint

**Payload Mapping**:
```typescript
// Maps question responses to expected field names
which_elements_are_you_planning_to_migrate_to_the_cloud: data[0]?.[0]?.[0] || '',
approximately_how_many_servers_do_you_intend_to_migrate: data[0]?.[1]?.[0] || '',
what_type_of_workloads_do_you_run: data[1]?.[1]?.[0]?.split(',') || [],
// ... additional mappings
```

### 3. Updated CloudMigrationBody Integration

**Location**: `src/components/CloudMigrationBody/CloudMigrationBody.tsx`

**Changes**:
- Replaced generic `QuestionAndAnswers` with `CloudMigrationQuestionAndAnswers`
- Maintains existing calculation logic and state management
- Preserves all existing functionality while using the enhanced component

### 4. Updated CloudMigrationForm Integration

**Location**: `src/components/CloudMigrationForm/CloudMigrationForm.tsx`

**Changes**:
- Uses new `handleSubmitCloudMigration` function from useForm hook
- Simplified form submission logic
- Maintains existing form validation and error handling

## Architecture Consistency

### Maintained Patterns from AI Readiness:
1. **File Structure**: Same component organization pattern
2. **State Management**: localStorage-based persistence
3. **Error Handling**: Consistent validation and error display
4. **Progress Tracking**: Section-based navigation
5. **Form Integration**: Same form submission flow
6. **API Integration**: Follows existing API route patterns

### Cloud Migration Specific Enhancements:
1. **Multi-select Questions**: Checkbox support for questions requiring multiple answers
2. **Cost Calculation Integration**: Seamless integration with existing cost calculation logic
3. **Payload Structure**: Matches the provided example form submission payload
4. **Question Type Detection**: Intelligent detection of multi-select vs single-select questions

## Form Submission Payload Structure

The implementation generates a payload matching the provided example:

```json
{
  "which_elements_are_you_planning_to_migrate_to_the_cloud": "All infrastructure components",
  "approximately_how_many_servers_do_you_intend_to_migrate": "10 – 50",
  "what_type_of_workloads_do_you_run": ["Web Applications"],
  "what_is_the_main_purpose_behind_your_decision_to_migrate_to_the_cloud": ["Enhance scalability"],
  "which_cloud_environments_are_you_planning_to_deploy": ["Staging environment"],
  "minimum_cost": 237000,
  "maximum_migration_cost": 308100,
  "firstname": "test",
  "lastname": "data",
  "email": "<EMAIL>",
  // ... additional fields
}
```

## Key Benefits

1. **Consistency**: Maintains architectural patterns from AI readiness feature
2. **Flexibility**: Supports both single and multi-select questions
3. **Type Safety**: Full TypeScript integration
4. **Maintainability**: Separate component prevents affecting AI readiness functionality
5. **Scalability**: Easy to extend for additional question types
6. **User Experience**: Intuitive multi-select interface with visual indicators

## Testing Recommendations

1. **Multi-select Questions**: Test questions that should allow multiple selections
2. **Single-select Questions**: Verify exclusive selection behavior
3. **Form Submission**: Validate payload structure matches expected format
4. **Cost Calculation**: Ensure calculation logic works with new component
5. **Responsive Design**: Test across mobile, tablet, and desktop breakpoints
6. **Error Handling**: Verify validation works for both question types

## Future Enhancements

1. **Question Type Configuration**: Add Strapi field to explicitly define multi-select questions
2. **Visual Indicators**: Enhanced UI for multi-select questions
3. **Answer Limits**: Support for maximum selection limits on multi-select questions
4. **Conditional Logic**: Support for conditional question display based on previous answers

## Files Modified/Created

### New Files:
- `src/components/CloudMigrationQuestionAndAnswers/CloudMigrationQuestionAndAnswers.tsx`
- `src/components/CloudMigrationQuestionAndAnswers/CloudMigrationQuestionAndAnswers.module.css`
- `src/components/CloudMigrationQuestionAndAnswers/index.ts`

### Modified Files:
- `src/hooks/useForm.ts` - Added `handleSubmitCloudMigration` function
- `src/components/CloudMigrationBody/CloudMigrationBody.tsx` - Updated to use new component
- `src/components/CloudMigrationForm/CloudMigrationForm.tsx` - Updated form submission logic
- `src/components/QuestionAndAnswers/QuestionAndAnswers.tsx` - Added variant prop (minimal change)

## Conclusion

The implementation successfully creates a cloud migration specific variant of the QuestionAndAnswers component while maintaining consistency with the existing codebase architecture. The solution supports both single and multi-select questions, integrates seamlessly with the existing cost calculation logic, and generates the correct payload structure for form submission.
