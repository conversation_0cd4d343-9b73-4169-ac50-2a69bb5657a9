@value variables: "@styles/variables.module.css";
@value brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, colorWhite, colorBlack, gray300 from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm, breakpoint-xl, breakpoint-xl-1024, breakpoint-xl-1440 from breakpoints;

.container {
  padding: 36px 0 80px 0;

  @media screen and (max-width: breakpoint-xl-1024) {
    padding: 36px 0 36px 0;
  }
}

.step_container {
  height: 45px;
  padding: 16px;
  display: flex;
  gap: 10px;

  font-weight: 500;
  font-size: 18px;
  line-height: 160%;
  letter-spacing: 0.36;
}

.hidden {
  display: none;
}

.section_wrapper {
  width: fit-content;
  display: flex;
  flex-direction: column;
  gap: 40px;
  margin: 0 auto;
  padding: 0 10px;

  @media screen and (min-width: 1200px) {
    width: 1192px;
  }
}

.heading>h2 {
  font-weight: 600;
  font-size: 32px;
  line-height: 132%;
  padding: 20px;
  border-bottom: 2px solid black;
}

.button_wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 40px;
}

.button_wrapper>button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 10px;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.button_wrapper>button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.button_wrapper_mobile {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 20px;
  font-weight: 500;
  font-size: 16px;
}

.button_wrapper_mobile>button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.button_wrapper_mobile>button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.error_message {
  color: #ff5656;
  font-size: 14px;
  font-weight: 500;
  margin-top: 10px;
  padding: 10px;
  background-color: #fff5f5;
  border: 1px solid #ff5656;
  border-radius: 4px;
}

.results_wrapper {
  width: fit-content;
  display: flex;
  flex-direction: column;
  gap: 40px;
  margin: 0 auto;
  padding: 40px 10px;
  text-align: center;

  @media screen and (min-width: 1200px) {
    width: 1192px;
  }
}

.cost_display {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px;
  border-radius: 16px;
  margin: 20px 0;
}

.cost_range {
  font-size: 48px;
  font-weight: 700;
  margin: 20px 0;
}

.cost_description {
  font-size: 18px;
  line-height: 1.6;
  margin: 20px 0;
}

.consultation_button {
  margin: 20px auto;
  padding: 16px 32px;
  background-color: brandColorOne;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.consultation_button:hover {
  background-color: brandColorTwo;
}

.restart_button {
  margin: 20px auto;
  padding: 12px 24px;
  background-color: transparent;
  color: brandColorOne;
  border: 2px solid brandColorOne;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.restart_button:hover {
  background-color: brandColorOne;
  color: white;
}

@media screen and (max-width: breakpoint-xl-1024) {
  .cost_range {
    font-size: 36px;
  }
  
  .cost_description {
    font-size: 16px;
  }
  
  .heading>h2 {
    font-size: 24px;
    padding: 16px;
  }
}
