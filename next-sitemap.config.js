/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: `${process.env.NEXT_PUBLIC_SITE_URL}`,
  generateRobotsTxt: false, // We manage robots.txt manually
  generateIndexSitemap: false, // Disables creating an index sitemap
  exclude: [
    '/api/*', // Exclude API routes
    '/sitemap', // Exclude our custom sitemap page from next-sitemap
  ],
  additionalPaths: async config => {
    // We'll let our custom API handle dynamic routes
    // This keeps the existing next-sitemap functionality for static pages
    return [];
  },
  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        disallow: ['/api/', '/admin/'],
      },
    ],
    additionalSitemaps: [`${process.env.NEXT_PUBLIC_SITE_URL}/api/sitemap`],
  },
};
