'use client';

import React, { useState } from 'react';
import useForm from '@hooks/useForm';
import Button from '@components/Button';
import styles from './CloudMigrationForm.module.css';

export default function CloudMigrationForm({
  formData,
  handleResult,
  handleVisibleSection,
}: any) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const initialValues = {
    firstName: '',
    lastName: '',
    emailAddress: '',
    companyName: '',
    phoneNumber: '',
    howCanWeHelpYou: '',
    consent: false,
  };

  const initialErrors = {
    firstName: { empty: false, invalid: false },
    lastName: { empty: false, invalid: false },
    emailAddress: { empty: false, invalid: false },
    companyName: { empty: false, invalid: false },
    phoneNumber: { empty: false, invalid: false },
    howCanWeHelpYou: { empty: false, invalid: false },
    consent: { empty: false, invalid: false },
  };

  const {
    values,
    errors,
    errorMessages,
    handleChange,
    handleSubmit: originalHandleSubmit,
  } = useForm(
    initialValues,
    initialErrors,
    'cloudMigration',
    'Cloud Migration Cost Calculator',
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Calculate the migration cost first
      const calculationResult = handleResult();

      if (calculationResult) {
        // Prepare form data with calculation results
        const formDataWithResults = {
          ...values,
          ...calculationResult.newResult,
          calculationData: calculationResult.data,
          secondary_source: 'Cloud Migration Cost Calculator',
        };

        // Submit the form
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_SITE_URL}/api/cloud-migration-cost-calculator/`,
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(formDataWithResults),
          },
        );
        handleVisibleSection(999); // Move to results

        if (response.ok) {
          // Move to results section
          handleVisibleSection(999); // Move to results
        } else {
          console.error('Error submitting form:', await response.json());
        }
      }
    } catch (error) {
      console.error('Error in form submission:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={styles.formWrapper}>
      <div className={styles.heading}>
        <h2>{formData?.title || 'Contact Details'}</h2>
      </div>

      <form onSubmit={handleSubmit} className={styles.form}>
        <div className={styles.formFields}>
          <div className={styles.personalDetailsWrapper}>
            <div className={`${styles.row} ${styles.firstRow}`}>
              <div className={styles.nameAndInputWrapper}>
                <label
                  htmlFor="firstName"
                  className={
                    errors.firstName.empty || errors.firstName.invalid
                      ? styles.errorLabel
                      : styles.formLabel
                  }
                >
                  {formData?.formFields?.fieldNameFor_FirstName ||
                    'First Name*'}
                </label>
                <input
                  type="text"
                  id="firstName"
                  name="firstName"
                  value={values.firstName}
                  onChange={e =>
                    handleChange({
                      name: e.target.name,
                      value: e.target.value,
                    })
                  }
                  className={`${styles.formInput} ${errors.firstName.empty || errors.firstName.invalid ? styles.errorInput : ''}`}
                  required
                />
              </div>

              <div className={styles.nameAndInputWrapper}>
                <label
                  htmlFor="lastName"
                  className={
                    errors.lastName.empty || errors.lastName.invalid
                      ? styles.errorLabel
                      : styles.formLabel
                  }
                >
                  {formData?.formFields?.fieldNameFor_LastName || 'Last Name*'}
                </label>
                <input
                  type="text"
                  id="lastName"
                  name="lastName"
                  value={values.lastName}
                  onChange={e =>
                    handleChange({
                      name: e.target.name,
                      value: e.target.value,
                    })
                  }
                  className={`${styles.formInput} ${errors.lastName.empty || errors.lastName.invalid ? styles.errorInput : ''}`}
                  required
                />
              </div>
            </div>

            <div className={styles.row}>
              <div className={styles.nameAndInputWrapper}>
                <label
                  htmlFor="emailAddress"
                  className={
                    errors.emailAddress.empty || errors.emailAddress.invalid
                      ? styles.errorLabel
                      : styles.formLabel
                  }
                >
                  {formData?.formFields?.fieldNameFor_EmailAddress ||
                    'Email Address*'}
                </label>
                <input
                  type="email"
                  id="emailAddress"
                  name="emailAddress"
                  value={values.emailAddress}
                  onChange={e =>
                    handleChange({
                      name: e.target.name,
                      value: e.target.value,
                    })
                  }
                  className={`${styles.formInput} ${errors.emailAddress.empty || errors.emailAddress.invalid ? styles.errorInput : ''}`}
                  required
                />
              </div>

              <div className={styles.nameAndInputWrapper}>
                <label
                  htmlFor="companyName"
                  className={
                    errors.companyName.empty || errors.companyName.invalid
                      ? styles.errorLabel
                      : styles.formLabel
                  }
                >
                  {formData?.formFields?.fieldNameFor_CompanyName ||
                    'Company Name*'}
                </label>
                <input
                  type="text"
                  id="companyName"
                  name="companyName"
                  value={values.companyName}
                  onChange={e =>
                    handleChange({
                      name: e.target.name,
                      value: e.target.value,
                    })
                  }
                  className={`${styles.formInput} ${errors.companyName.empty || errors.companyName.invalid ? styles.errorInput : ''}`}
                  required
                />
              </div>
            </div>

            <div className={styles.row}>
              <div className={styles.nameAndInputWrapper}>
                <label
                  htmlFor="phoneNumber"
                  className={
                    errors.phoneNumber.empty || errors.phoneNumber.invalid
                      ? styles.errorLabel
                      : styles.formLabel
                  }
                >
                  {formData?.formFields?.fieldNameFor_PhoneNumber ||
                    'Phone Number*'}
                </label>
                <input
                  type="tel"
                  id="phoneNumber"
                  name="phoneNumber"
                  value={values.phoneNumber}
                  onChange={e =>
                    handleChange({
                      name: e.target.name,
                      value: e.target.value,
                    })
                  }
                  className={`${styles.formInput} ${errors.phoneNumber.empty || errors.phoneNumber.invalid ? styles.errorInput : ''}`}
                  required
                />
              </div>

              <div className={styles.nameAndInputWrapper}>
                <label htmlFor="howCanWeHelpYou" className={styles.formLabel}>
                  {formData?.formFields?.fieldNameFor_HowCanWeHelpYou ||
                    'How Can We Help You?'}
                </label>
                <input
                  type="text"
                  id="howCanWeHelpYou"
                  name="howCanWeHelpYou"
                  value={values.howCanWeHelpYou}
                  onChange={e =>
                    handleChange({
                      name: e.target.name,
                      value: e.target.value,
                    })
                  }
                  className={styles.formInput}
                />
              </div>
            </div>

            <div className={`${styles.row} ${styles.submitButtonRow}`}>
              <div className={styles.consentRow}>
                <div
                  className={
                    errors.consent.empty
                      ? styles.errorLabel_consentText
                      : styles.consentText
                  }
                  onClick={() =>
                    handleChange({
                      name: 'consent',
                      value: !values.consent,
                      type: 'checkbox',
                      checked: !values.consent,
                    })
                  }
                >
                  <input
                    type="checkbox"
                    name="consent"
                    checked={values.consent}
                    onChange={e =>
                      handleChange({
                        name: e.target.name,
                        value: e.target.checked,
                        type: 'checkbox',
                        checked: e.target.checked,
                      })
                    }
                    required
                  />
                  {formData?.formFields?.consentText ||
                    'I consent to processing of my personal data entered above for Maruti Techlabs to contact me.*'}
                </div>
              </div>

              {errors.consent.empty && (
                <div className={styles.errorMessages}>
                  You must consent to processing of your personal data
                </div>
              )}

              {isSubmitting ? (
                <div className={styles.container_spinner}>
                  <div className={styles.spinner}></div>
                </div>
              ) : (
                <Button
                  type="submit"
                  className={styles.result_button}
                  label={
                    formData?.button?.title || 'Check my Cloud Migration Cost'
                  }
                />
              )}
            </div>
          </div>
        </div>
      </form>
    </div>
  );
}
