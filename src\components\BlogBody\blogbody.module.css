@value variables: "@styles/variables.module.css";
@value fontWeight600, fontWeight400, fontWeight500, gray300, gray800, gray900, oneSpace fontWeight700, bodyTextXXSmall, halfSpace, twoSpace, threeSpace, fourSpace, fiveSpace, colorBlack, colorWhite, fifteenSpace, grayBorder, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive, bodyTextXXXSSmall from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-xl-1309, breakpoint-md, breakpoint-xl-2000, breakpoint-xl-1800, breakpoint-sm-450, breakpoint-sm-550, breakpoint-sm-320, breakpoint-xl-1024, breakpoint-xl-1600, breakpoint-xl-1440, breakpoint-lg, breakpoint-lg-991px from breakpoints;

.bannerBetweenContent {
  padding: 5px 20px;
  background-image: linear-gradient(to right,
      brandColorFive 0%,
      brandColor<PERSON>hree 60%,
      brandColorOne 100%);
}

.bannerBetweenContent .bannerBtn {
  background-color: #555555;
  border: none;
  color: colorWhite;
  padding: 10px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  margin: 4px 2px;
  cursor: pointer;
}

.blogbody__row {
  margin: 0 150px;

  @media screen and (max-width: breakpoint-xl-1600) {
    margin: 0 80px;
  }

  @media screen and (max-width: breakpoint-xl-1440) {
    margin: 0 40px;
  }

  @media screen and (max-width: breakpoint-lg) {
    display: flex;
    justify-content: center;
    margin: 0;
  }
}

.blogbody__toc {
  padding-right: 10px;

  @media screen and (max-width: breakpoint-lg) {
    display: contents;
  }
}

.blogbody__content * {
  font-family: var(--font-poppins) !important;
}

.blogbody__content {
  padding-top: 4.4rem;
  max-width: 660px;
  margin: 0 auto;

  @media screen and (max-width: breakpoint-lg) {
    max-width: 992px;
  }
}

@media screen and (max-width: breakpoint-lg) {
  .blogbody__content {
    width: 80%;
    padding-top: 1.5em;
  }
}

@media screen and (max-width: breakpoint-sm) {
  .blogbody__content {
    width: 90%;
    margin: 0 auto;
  }
}


.blogbody__content .blogbody__content__h2 {
  font-weight: fontWeight600;
  font-size: 24px;
  scroll-margin-top: 67px;
  padding-top: 10px;
  text-transform: capitalize;
}

.blogbody__content iframe {
  width: clamp(660px, 1.6vw, 747px);
  height: clamp(371px, 2vw, 420.297px);
}

@media screen and (max-width: var(--breakpoint-sm)) {
  .blogbody__content iframe {
    width: clamp(288px, 85vw, 656px) !important;
    height: clamp(162px, 47.8vw, 369px) !important;
  }
}

@media screen and (min-width: var(--breakpoint-sm)) and (max-width: 825px) {
  .blogbody__content iframe {
    width: clamp(489.594px, 78.8vw, 660px) !important;
    height: clamp(275.469px, 43vw, 371.5px) !important;
  }
}

@media screen and (min-width: 993px) {
  .blogbody__content iframe {
    width: clamp(496px, 50vw, 660px) !important;
    height: clamp(279px, 28vw, 371px) !important;
  }
}

.blogbody__content a {
  text-decoration: underline !important;
  text-decoration-color: brandColorThree !important;
}

.blogbody__content a span {
  text-decoration: underline !important;
  text-decoration-color: brandColorThree !important;
}

.blogbody__content h1 {
  font-size: 36px !important;
  line-height: 115%;

  color: gray900;
}

.blogbody__content h2 {
  font-size: 24px !important;
  font-weight: fontWeight600;
  line-height: 180%;
  color: gray900;
}

.blogbody__content h3 {
  font-size: 18px !important;
  line-height: 180%;
  color: gray900 !important;

  font-weight: fontWeight600;
}

.blogbody__content h4 {
  font-size: 16px !important;
  line-height: 180%;
  color: gray900 !important;
  font-weight: fontWeight600;
}

.blogbody__content h4 span strong {
  font-weight: fontWeight600 !important;
}

.blogbody__content div {
  font-size: 16px !important;
  margin-bottom: 12px;
}

.blogbody__content div img {
  width: 100%;
}

.blogbody__content blockquote {
  display: block;
  margin: 1.5em 0.711238vw 1.5em 0.711238vw;
}

.blogbody__content blockquote p {
  display: inline;
  padding: 0px 12px;
}

.blogbody__content blockquote p span {
  font-weight: fontWeight600;
}

.blogbody__content blockquote::before {
  box-sizing: border-box;
  color: #525252;
  content: '\201C';
  display: inline;
  font-family: 'AmericanTypewriter';
  font-size: 4em;
  height: auto;
  line-height: 0.1em;
  vertical-align: -0.4em !important;
  width: auto;
}

.blogbody__content blockquote::after {
  box-sizing: border-box;
  color: #525252;
  content: '\201D';
  display: inline;
  font-family: 'AmericanTypewriter';
  font-size: 4em;
  height: auto;
  line-height: 0.1em;
  vertical-align: -0.4em !important;
  width: auto;
}

.blogbody__para img {
  width: 100%;
}

.blogbody__para p {
  font-weight: fontWeight500;
  font-size: 16px;
  color: gray900 !important;
  line-height: 180%;
}

.blogbody__para p a {
  color: brandColorThree !important;
  font-size: inherit;
  font-weight: inherit;
  text-decoration: underline !important;
  text-decoration-color: brandColorThree !important;
}

.blogbody__para p a u {
  color: brandColorThree !important;
}

.blogbody__para p a span {
  color: brandColorThree !important;
}

.blogbody__para p span {
  font-weight: fontWeight400;
  font-size: inherit;
  color: gray900 !important;
  line-height: 28.8px;
}

.blogbody__para strong {
  font-size: inherit;
  font-weight: fontWeight600;
}

.blogbody__para strong u {
  color: brandColorThree !important;
  font-size: inherit;
}

.blogbody__para ul li span {
  font-size: inherit;
  line-height: 28.8px;
}

.blogbody__para strong a {
  color: brandColorThree;
  text-decoration: underline !important;
  text-decoration-color: brandColorThree !important;
  font-size: inherit;
  font-weight: inherit;
}

.blogbody__para ol li span {
  font-size: inherit;
  line-height: 28.8px;
}

.blogbody__para ul li {
  font-size: inherit;
  line-height: 28.8px;
}

.blogbody__para table tr:nth-child(even) {
  background-color: rgb(233 233 233 / 77%);
}

.blogbody__para table tr td {
  padding: 4px 6px;
  border: 1px solid #cccccc;
}

.blogbody__para pre {
  background-color: #f5f2f0;
}

.blogbody__para code {
  color: gray900;
  display: block;
  padding: 6px 10px;
}

.tweet_body {
  margin: 1rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.tweet_body .tweet {
  font-style: normal;
  font-weight: 400;
  font-size: 24px;
  line-height: 150%;
  color: #1d9bf0;
  max-width: 441px;
  text-decoration: underline;
  cursor: pointer;
}

.tweet_body button {
  background: #1d9bf0 !important;
  border-color: #1d9bf0 !important;
  padding: 0px 16px;
  width: 104px;
  border-radius: 100px;
  height: 36px;
  font-family: 'Work Sans', sans-serif;
  font-style: normal;
  font-weight: fontWeight700;
  font-size: 10px;
  line-height: 24px;
  color: colorWhite !important;
}

.tweet_body button:hover {
  background: #1d9bf0;
  border-color: #1d9bf0 !important;
  color: colorWhite;
}

.blogbody__socialicons {
  padding-left: 10px;
  max-width: 320px; /* Increased from 228px for better form layout */
  margin-top: 80px;

  @media (max-width: breakpoint-lg) {
    display: none;
  }

  /* Enhanced responsive design for larger screens */
  @media screen and (min-width: breakpoint-xl-1440) {
    max-width: 380px;
    padding-left: 20px;
  }

  @media screen and (min-width: breakpoint-xl-1600) {
    max-width: 420px;
    padding-left: 30px;
  }
}

.socialIconsWrapper {
  position: sticky;
  position: -webkit-sticky;
  top: 90px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.serviceSection {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.services_title {
  color: gray900;
  font-size: 18px; /* Reduced from 20px */
  font-style: normal;
  font-weight: 600;
  line-height: 140%; /* Reduced from 148% */
}

.services_box_container {
  position: relative;
  padding: 24px;
  display: flex;
  gap: 10px;
  flex-direction: column;
  border-radius: 6px;
  background: gray300;
  overflow-wrap: break-word;
  text-decoration: none;
  transition: 0.2s linear;

  /* Enhanced responsive padding for larger screens */
  @media screen and (min-width: breakpoint-xl-1440) {
    padding: 28px;
    gap: 12px;
  }

  @media screen and (min-width: breakpoint-xl-1600) {
    padding: 32px;
    gap: 14px;
  }
}

.services_box_container:hover {
  box-shadow:
    3px 3px 13px 0px #ffac7d82,
    6px -2px 11px 0px #ff72ae8f,
    -6px 3px 11px 1px #ffbb0057;
}

.box_border_gradient:hover {
  box-shadow: none;
}

.box_border_gradient::before {
  content: '';
  position: absolute;
  top: 1px;
  left: -1px;
  right: -1.5px;
  bottom: 0px;
  border-radius: 3px;
  padding: 2px;
  /* This is the width of the border */
  background: linear-gradient(93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%);
  -webkit-mask:
    linear-gradient(colorWhite 0 0) content-box,
    linear-gradient(colorWhite 0 0);
  -webkit-mask-composite: destination-out;
  mask-composite: exclude;
}

.service_title {
  color: colorBlack;
  font-size: 18px; /* Reduced from 20px */
  font-weight: 600;
  line-height: 130%;
  letter-spacing: 0.36px; /* Reduced proportionally */

  /* Specific responsive typography for different screen sizes */
  @media screen and (min-width: breakpoint-xl-1024) and (max-width: 1199px) {
    font-size: 18px; /* Reduced from 21px */
    letter-spacing: 0.36px;
    line-height: 130%;
  }

  @media screen and (min-width: breakpoint-xl) and (max-width: 1439px) {
    font-size: 19px; /* Reduced from 22px */
    letter-spacing: 0.38px;
    line-height: 132%;
  }

  @media screen and (min-width: breakpoint-xl-1440) and (max-width: 1599px) {
    font-size: 20px; /* Reduced from 23px */
    letter-spacing: 0.40px;
    line-height: 134%;
  }

  @media screen and (min-width: breakpoint-xl-1600) {
    font-size: 21px; /* Reduced from 24px */
    letter-spacing: 0.42px;
    line-height: 136%;
  }
}

.service_description {
  color: colorBlack;
  font-size: 13px; /* Reduced from 15px */
  font-weight: 400;
  line-height: 1.35; /* Reduced from 1.4 */

  /* Specific responsive typography for different screen sizes */
  @media screen and (min-width: breakpoint-xl-1024) and (max-width: 1199px) {
    font-size: 13px; /* Reduced from 15px */
    line-height: 1.35;
  }

  @media screen and (min-width: breakpoint-xl) and (max-width: 1439px) {
    font-size: 14px; /* Reduced from 15.5px */
    line-height: 1.4;
  }

  @media screen and (min-width: breakpoint-xl-1440) and (max-width: 1599px) {
    font-size: 14px; /* Reduced from 16px */
    line-height: 1.4;
  }

  @media screen and (min-width: breakpoint-xl-1600) {
    font-size: 15px; /* Reduced from 16.5px */
    line-height: 1.45;
  }
}

.form {
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 10;

  /* Enhanced responsive spacing for larger screens */
  @media screen and (min-width: breakpoint-xl-1440) {
    gap: 12px;
  }

  @media screen and (min-width: breakpoint-xl-1600) {
    gap: 14px;
  }
}

.submitButton {
  background-image: linear-gradient(gray300, gray300),
    linear-gradient(93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%) !important;
  padding: 8px 16px !important; /* Reduced from 10px 20px */
  color: colorBlack !important;
  border-radius: 3px; /* Reduced from 4px */
  font-weight: 600;
  font-size: 13px; /* Reduced from 15px */
  transition: all 0.2s ease;
  cursor: pointer;

  /* Specific responsive styling for different screen sizes */
  @media screen and (min-width: breakpoint-xl-1024) and (max-width: 1199px) {
    padding: 9px 18px !important; /* Reduced from 11px 22px */
    border-radius: 3px;
    font-size: 13px;
  }

  @media screen and (min-width: breakpoint-xl) and (max-width: 1439px) {
    padding: 10px 20px !important; /* Reduced from 12px 24px */
    border-radius: 3px;
    font-size: 14px; /* Reduced from 15.5px */
  }

  @media screen and (min-width: breakpoint-xl-1440) and (max-width: 1599px) {
    padding: 11px 22px !important; /* Reduced from 13px 26px */
    border-radius: 4px;
    font-size: 14px; /* Reduced from 16px */
  }

  @media screen and (min-width: breakpoint-xl-1600) {
    padding: 12px 24px !important; /* Reduced from 14px 28px */
    border-radius: 4px;
    font-size: 15px; /* Reduced from 16.5px */
  }
}

.container_spinner {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 3px;
  font-weight: 600;
  font-size: 14px; /* Reduced from 16px */
  line-height: 22px; /* Reduced from 24px */
  border: 2px solid transparent;
  cursor: pointer;
  padding: 6px 14px; /* Reduced from 8px 18px */
  /* width: 170px; */
  /* height: 68px; */

  background-image: linear-gradient(gray300, gray300),
    linear-gradient(93.12deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  z-index: 1;

  @media screen and (max-width: breakpoint-md) {
    width: 100%;
  }

  /* Enhanced responsive styling for larger screens */
  @media screen and (min-width: breakpoint-xl-1440) {
    padding: 8px 18px; /* Reduced from 10px 22px */
    border-radius: 3px;
    font-size: 15px; /* Reduced from 17px */
  }

  @media screen and (min-width: breakpoint-xl-1600) {
    padding: 10px 22px; /* Reduced from 12px 26px */
    border-radius: 4px;
    font-size: 16px; /* Reduced from 18px */
  }
}

.spinner {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: linear-gradient(93deg,
      brandColorOne 0%,
      brandColorTwo 30.56%,
      brandColorThree 53.47%,
      brandColorFour 75.75%,
      brandColorFive 100%);

  -webkit-mask-image: radial-gradient(circle,
      rgba(0, 0, 0, 0) 55%,
      rgba(0, 0, 0, 1) 60%);
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.submitButton>div {
  font-size: 16px; /* Reduced from 20px */
  font-style: normal;
  font-weight: 600;
  line-height: 150%; /* Reduced from 160% */
}

.submitButton::before {
  border-radius: 6px;
  padding: 2px;
}

.formFields {
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 140%;
  margin-bottom: 2px;

  /* For screen width between 1024px and 1218px */
  @media screen and (min-width: 1024px) and (max-width: 1218px) {
    font-size: 12px;
    line-height: 135%;
    margin-bottom: 1px; /* Reduced from 2px */
  }

  /* For screen width 1440px and height exactly 1218px */
  @media screen and (min-width: 1440px) and (max-width: 1440px) and (min-height: 1218px) and (max-height: 1218px) {
    font-size: 13px;
    line-height: 145%;
    margin-bottom: 2px; /* Reduced from 3px */
  }

  /* Existing breakpoints can be kept or updated as needed */
  @media screen and (min-width: 1024px) and (max-width: 1199px) {
    font-size: 12px;
    margin-bottom: -6px;
    line-height: 140%;
  }

  @media screen and (min-width: 1200px) and (max-width: 1439px) {
    font-size: 13px;
    margin-bottom: -8px;
    line-height: 145%;
  }

  @media screen and (min-width: 1440px) and (max-width: 1599px) {
    font-size: 13px;
    margin-bottom: -8px;
    line-height: 148%;
  }

  @media screen and (min-width: 1600px) {
    font-size: 14px;
    margin-bottom: -10px;
    line-height: 150%;
  }
}


.formInput {
  border: 1px solid gray800 !important;
  border-radius: 3px;
  height: 36px; /* Reduced from 41px */
  border: 0;
  padding: 8px; /* Reduced from 10px */
  font-size: 12px; /* Reduced from 14px */
  transition: all 0.2s ease;

  /* Enhanced responsive styling for larger screens */
  @media screen and (min-width: breakpoint-xl-1440) {
    height: 38px; /* Reduced from 44px */
    padding: 10px; /* Reduced from 12px */
    font-size: 13px; /* Reduced from 15px */
    border-radius: 3px;
  }

  @media screen and (min-width: breakpoint-xl-1600) {
    height: 40px; /* Reduced from 46px */
    padding: 12px; /* Reduced from 14px */
    font-size: 14px; /* Reduced from 16px */
    border-radius: 4px;
  }
}

.formInput:focus-visible {
  border: 0;
  margin: 0;
}

.error {
  color: rgb(249, 84, 73);
  font-size: 14px;
  font-weight: 600;
}

.ph_number_countries_input_services_page {
  width: 100% !important;
  border: 1px solid gray800 !important;
  border-radius: 3px;
  height: 36px !important; /* Reduced from 41px */
  padding: 8px; /* Reduced from 10px */
  font-size: 12px; /* Reduced from 14px */
  transition: all 0.2s ease;

  /* Enhanced responsive styling for larger screens */
  @media screen and (min-width: breakpoint-xl-1440) {
    height: 38px !important; /* Reduced from 44px */
    padding: 10px; /* Reduced from 12px */
    font-size: 13px; /* Reduced from 15px */
    border-radius: 3px;
  }

  @media screen and (min-width: breakpoint-xl-1600) {
    height: 40px !important; /* Reduced from 46px */
    padding: 12px; /* Reduced from 14px */
    font-size: 14px; /* Reduced from 16px */
    border-radius: 4px;
  }
}

.ph_number_countries_button_services_page {
  border: 1px solid gray800 !important;
  background-color: white !important;
  transition: all 0.2s ease;

  /* Enhanced responsive styling for larger screens */
  @media screen and (min-width: breakpoint-xl-1440) {
    border-radius: 4px;
  }

  @media screen and (min-width: breakpoint-xl-1600) {
    border-radius: 5px;
  }
}

.ph_number_countries_dropdown_services_page {
  background-color: white !important;
}

.errorInput {
  border: 1px solid #ff0000 !important;
}

.errorMessages {
  font-weight: 500;
  font-size: 16px;
  line-height: 25.6px;
  color: #ff0000;
}

.errorLabel {
  color: #ff0000;
  @media screen and (min-width: 1024px) and (max-width: 1199px) {
    font-size: 12px;
    margin-bottom: -6px;
    line-height: 140%;
  }

  @media screen and (min-width: 1200px) and (max-width: 1439px) {
    font-size: 13px;
    margin-bottom: -8px;
    line-height: 145%;
  }

  @media screen and (min-width: 1440px) and (max-width: 1599px) {
    font-size: 13px;
    margin-bottom: -8px;
    line-height: 148%;
  }

  @media screen and (min-width: 1600px) {
    font-size: 14px;
    margin-bottom: -10px;
    line-height: 150%;
  }
}

.ph_number_countries_button_services_page>div,
.ph_number_countries_button_services_page>div:hover {
  background-color: white !important;
}

.ph_number_countries_button_services_page>div>div>div {
  border-bottom: none !important;
  border-top: 5px solid black !important;
}